/// 바라 부스 매니저 - 데이터 백업/복원 서비스
///
/// 앱의 모든 데이터를 백업하고 복원하는 서비스입니다.
/// - 로컬 데이터베이스 전체 백업
/// - 사용자 설정 백업
/// - 데이터 복원
/// - 백업 데이터 검증
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;


import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import '../services/database_service.dart';
import '../models/event.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/seller.dart';
import '../models/sales_log.dart';
import '../models/prepayment.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/prepayment_product_link.dart';
import '../utils/logger_utils.dart';

/// 백업 데이터 구조
class BackupData {
  final String version;
  final String userId;
  final DateTime createdAt;
  final Map<String, dynamic> userData;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> settings;

  BackupData({
    required this.version,
    required this.userId,
    required this.createdAt,
    required this.userData,
    required this.localData,
    required this.settings,
  });

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'userData': userData,
      'localData': localData,
      'settings': settings,
    };
  }

  factory BackupData.fromJson(Map<String, dynamic> json) {
    return BackupData(
      version: json['version'] ?? '1.0.0',
      userId: json['userId'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      userData: json['userData'] ?? {},
      localData: json['localData'] ?? {},
      settings: json['settings'] ?? {},
    );
  }
}

/// 데이터 백업/복원 서비스
class BackupService {
  static const String _tag = 'BackupService';
  static const String _backupVersion = '1.0.0';

  final DatabaseService _databaseService;

  BackupService(this._databaseService);

  /// 모든 데이터 백업
  Future<BackupData> createBackup() async {
    try {
      LoggerUtils.logInfo('전체 데이터 백업 시작', tag: _tag);

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('로그인된 사용자가 없습니다');
      }

      // 1. 사용자 데이터 백업 (Firestore)
      final userData = await _backupUserData(user.uid);

      // 2. 로컬 데이터 백업 (SQLite)
      final localData = await _backupLocalData();

      // 3. 앱 설정 백업 (SharedPreferences)
      final settings = await _backupSettings();

      final backupData = BackupData(
        version: _backupVersion,
        userId: user.uid,
        createdAt: DateTime.now(),
        userData: userData,
        localData: localData,
        settings: settings,
      );

      LoggerUtils.logInfo('전체 데이터 백업 완료', tag: _tag);
      return backupData;

    } catch (e, stackTrace) {
      LoggerUtils.logError('데이터 백업 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 사용자 데이터 백업 (Firestore)
  Future<Map<String, dynamic>> _backupUserData(String userId) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final userData = <String, dynamic>{};

      // 사용자 기본 정보
      final userDoc = await firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        userData['userInfo'] = userDoc.data();
      }

      // 구독 정보
      final subscriptionDoc = await firestore
          .collection('users')
          .doc(userId)
          .collection('subscriptions')
          .doc('current')
          .get();
      if (subscriptionDoc.exists) {
        userData['subscription'] = subscriptionDoc.data();
      }

      // 행사/상품 등 앱 데이터는 로컬 DB 백업으로 충분하므로 Firestore에서 별도 수집하지 않습니다.
      // (사용자 기본 정보, 구독 정보만 포함)

      LoggerUtils.logInfo('사용자 데이터 백업 완료', tag: _tag);
      return userData;

    } catch (e, stackTrace) {
      LoggerUtils.logError('사용자 데이터 백업 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return {};
    }
  }

  /// 로컬 데이터 백업 (SQLite)
  Future<Map<String, dynamic>> _backupLocalData() async {
    try {
      final localData = <String, dynamic>{};

      // 행사 데이터
      final events = await _databaseService.getAllEvents();
      localData['events'] = events.map((e) => e.toJson()).toList();

      // 카테고리 데이터
      final categories = await _databaseService.getAllCategories();
      localData['categories'] = categories.map((c) => c.toJson()).toList();

      // 상품 데이터
      final products = await _databaseService.getAllProducts();
      localData['products'] = products.map((p) => p.toJson()).toList();

      // 판매자 데이터
      final sellers = await _databaseService.getAllSellers();
      localData['sellers'] = sellers.map((s) => s.toJson()).toList();

      // 판매 기록
      final salesLogs = await _databaseService.getAllSalesLogs();
      localData['salesLogs'] = salesLogs.map((s) => s.toJson()).toList();

      // 선결제 데이터
      final prepayments = await _databaseService.getAllPrepayments();
      localData['prepayments'] = prepayments.map((p) => p.toJson()).toList();

      // 선결제 가상 상품
      final virtualProducts = await _databaseService.getAllPrepaymentVirtualProducts();
      localData['prepaymentVirtualProducts'] = virtualProducts.map((v) => v.toMap()).toList();

      // 선결제 상품 링크
      final productLinks = await _databaseService.getAllPrepaymentProductLinks();
      localData['prepaymentProductLinks'] = productLinks.map((l) => l.toJson()).toList();

      // 세트할인
      final setDiscounts = await _databaseService.getAllSetDiscounts();
      localData['setDiscounts'] = setDiscounts;

      // 체크리스트 템플릿
      final checklistTemplates = await _databaseService.getAllChecklistTemplates();
      localData['checklistTemplates'] = checklistTemplates;

      // 체크리스트 아이템 (체크 여부)
      final checklistItems = await _databaseService.getAllChecklistItems();
      localData['checklistItems'] = checklistItems;

      // 목표수익
      final revenueGoals = await _databaseService.getAllRevenueGoals();
      localData['revenueGoals'] = revenueGoals;

      // 닉네임 및 프로필 이미지
      final nicknames = await _databaseService.getAllNicknames();
      localData['nicknames'] = nicknames;

      // 판매 데이터 (sales_table)
      final salesTable = await _databaseService.getAllSalesTable();
      localData['salesTable'] = salesTable;

      LoggerUtils.logInfo('로컬 데이터 백업 완료', tag: _tag);
      return localData;

    } catch (e, stackTrace) {
      LoggerUtils.logError('로컬 데이터 백업 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return {};
    }
  }

  /// 앱 설정 백업 (SharedPreferences)
  Future<Map<String, dynamic>> _backupSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settings = <String, dynamic>{};

      // 모든 설정 키 가져오기
      final keys = prefs.getKeys();
      for (final key in keys) {
        // 민감한 정보는 제외
        if (key.contains('password') ||
            key.contains('token') ||
            key.contains('secret') ||
            key.contains('google_drive')) {
          continue;
        }

        final value = prefs.get(key);
        if (value != null) {
          settings[key] = value;
        }
      }

      // 결제수단 데이터 별도 백업 (PaymentMethodsProvider에서 관리)
      try {
        final paymentMethodsJson = prefs.getString('payment_methods');
        if (paymentMethodsJson != null) {
          settings['payment_methods'] = paymentMethodsJson;
        }
      } catch (e) {
        LoggerUtils.logWarning('결제수단 백업 실패', tag: _tag, error: e);
      }

      LoggerUtils.logInfo('앱 설정 백업 완료: ${settings.length}개 항목', tag: _tag);
      return settings;

    } catch (e, stackTrace) {
      LoggerUtils.logError('앱 설정 백업 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return {};
    }
  }

  /// 백업 데이터 복원
  Future<bool> restoreBackup(BackupData backupData) async {
    try {
      LoggerUtils.logInfo('백업 데이터 복원 시작', tag: _tag);

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('로그인된 사용자가 없습니다');
      }

      // 백업 데이터 검증
      if (!_validateBackupData(backupData)) {
        throw Exception('백업 데이터가 유효하지 않습니다');
      }

      // 1. 로컬 데이터 복원
      await _restoreLocalData(backupData.localData);

      // 2. 앱 설정 복원
      await _restoreSettings(backupData.settings);

      // 3. 사용자 데이터 복원 (선택적)
      // Firestore 데이터는 복원하지 않음 (기존 데이터 보존)

      LoggerUtils.logInfo('백업 데이터 복원 완료', tag: _tag);
      return true;

    } catch (e, stackTrace) {
      LoggerUtils.logError('백업 데이터 복원 실패', tag: _tag, error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 백업 데이터 검증
  bool _validateBackupData(BackupData backupData) {
    try {
      // 기본 필드 확인
      if (backupData.version.isEmpty || backupData.userId.isEmpty) {
        return false;
      }

      // 현재 사용자와 백업 사용자 일치 확인
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null || currentUser.uid != backupData.userId) {
        LoggerUtils.logWarning('백업 사용자와 현재 사용자가 다름', tag: _tag);
        return false;
      }

      return true;
    } catch (e) {
      LoggerUtils.logError('백업 데이터 검증 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 로컬 데이터 복원
  Future<void> _restoreLocalData(Map<String, dynamic> localData) async {
    try {
      // 기존 데이터 삭제 (선택적)
      // await _databaseService.clearAllData();

      // 행사 데이터 복원
      if (localData['events'] != null) {
        final eventsList = localData['events'] as List;
        for (final eventJson in eventsList) {
          final event = Event.fromJson(eventJson);
          await _databaseService.insertOrUpdateEvent(event);
        }
      }

      // 카테고리 데이터 복원
      if (localData['categories'] != null) {
        final categoriesList = localData['categories'] as List;
        for (final categoryJson in categoriesList) {
          final category = Category.fromJson(categoryJson);
          await _databaseService.insertOrUpdateCategory(category);
        }
      }

      // 상품 데이터 복원
      if (localData['products'] != null) {
        final productsList = localData['products'] as List;
        for (final productJson in productsList) {
          final product = Product.fromJson(productJson);
          await _databaseService.insertOrUpdateProduct(product);
        }
      }

      // 판매자 데이터 복원
      if (localData['sellers'] != null) {
        final sellersList = localData['sellers'] as List;
        for (final sellerJson in sellersList) {
          final seller = Seller.fromJson(sellerJson);
          await _databaseService.insertOrUpdateSeller(seller);
        }
      }

      // 판매 기록 복원
      if (localData['salesLogs'] != null) {
        final salesLogsList = localData['salesLogs'] as List;
        for (final salesLogJson in salesLogsList) {
          final salesLog = SalesLog.fromJson(salesLogJson);
          await _databaseService.insertOrUpdateSalesLog(salesLog);
        }
      }

      // 선결제 데이터 복원
      if (localData['prepayments'] != null) {
        final prepaymentsList = localData['prepayments'] as List;
        for (final prepaymentJson in prepaymentsList) {
          final prepayment = Prepayment.fromJson(prepaymentJson);
          await _databaseService.insertOrUpdatePrepayment(prepayment);
        }
      }

      // 선결제 가상 상품 복원
      if (localData['prepaymentVirtualProducts'] != null) {
        final virtualProductsList = localData['prepaymentVirtualProducts'] as List;
        for (final virtualProductJson in virtualProductsList) {
          final virtualProduct = PrepaymentVirtualProduct.fromJson(virtualProductJson);
          await _databaseService.insertOrUpdatePrepaymentVirtualProduct(virtualProduct);
        }
      }

      // 선결제 상품 링크 복원
      if (localData['prepaymentProductLinks'] != null) {
        final productLinksList = localData['prepaymentProductLinks'] as List;
        for (final productLinkJson in productLinksList) {
          final productLink = PrepaymentProductLink.fromJson(productLinkJson);
          await _databaseService.insertOrUpdatePrepaymentProductLink(productLink);
        }
      }

      // 세트할인 복원
      if (localData['setDiscounts'] != null) {
        final setDiscountsList = localData['setDiscounts'] as List;
        for (final setDiscountMap in setDiscountsList) {
          final db = await _databaseService.database;
          await db.insert('set_discounts', setDiscountMap, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      }

      // 체크리스트 템플릿 복원
      if (localData['checklistTemplates'] != null) {
        final templatesList = localData['checklistTemplates'] as List;
        for (final templateMap in templatesList) {
          final db = await _databaseService.database;
          await db.insert('checklist_templates', templateMap, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      }

      // 체크리스트 아이템 복원
      if (localData['checklistItems'] != null) {
        final itemsList = localData['checklistItems'] as List;
        for (final itemMap in itemsList) {
          final db = await _databaseService.database;
          await db.insert('checklist_items', itemMap, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      }

      // 목표수익 복원
      if (localData['revenueGoals'] != null) {
        final goalsList = localData['revenueGoals'] as List;
        for (final goalMap in goalsList) {
          final db = await _databaseService.database;
          await db.insert('revenue_goals', goalMap, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      }

      // 닉네임 복원
      if (localData['nicknames'] != null) {
        final nicknamesList = localData['nicknames'] as List;
        for (final nicknameMap in nicknamesList) {
          final db = await _databaseService.database;
          await db.insert('nicknames', nicknameMap, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      }

      // 판매 데이터 복원
      if (localData['salesTable'] != null) {
        final salesTableList = localData['salesTable'] as List;
        for (final salesMap in salesTableList) {
          final db = await _databaseService.database;
          await db.insert('sales_table', salesMap, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      }

      LoggerUtils.logInfo('로컬 데이터 복원 완료', tag: _tag);

    } catch (e, stackTrace) {
      LoggerUtils.logError('로컬 데이터 복원 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 앱 설정 복원
  Future<void> _restoreSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      for (final entry in settings.entries) {
        final key = entry.key;
        final value = entry.value;

        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        } else if (value is List<String>) {
          await prefs.setStringList(key, value);
        }
      }

      LoggerUtils.logInfo('앱 설정 복원 완료: ${settings.length}개 항목', tag: _tag);

    } catch (e, stackTrace) {
      LoggerUtils.logError('앱 설정 복원 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 백업 데이터에서 누락된 메서드들을 위한 확장
  /// (DatabaseService에서 필요한 메서드들이 없을 경우를 대비)
}
